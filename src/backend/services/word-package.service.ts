import type { Language, Difficulty } from '@prisma/client';
import { PartsOfSpeech } from '@prisma/client';
import type { WordPackageRepository } from '@/backend/repositories/word-package.repository';
import type { CollectionService } from './collection.service';
import type { WordService } from './word.service';
import {
	WordPackage,
	WordPackageWithStats,
	CreateWordPackageInput,
	WordPackageFilter,
	WordPackageStats,
} from '@/models/word-package';

export interface WordPackageService {
	getAvailablePackages(
		userId: string,
		filter?: WordPackageFilter,
		limit?: number
	): Promise<WordPackageWithStats[]>;
	getUserPackages(
		userId: string,
		limit?: number,
		source_language?: Language,
		target_language?: Language
	): Promise<any[]>;
	getPackageById(id: string): Promise<WordPackage | null>;
	createPackage(data: CreateWordPackageInput): Promise<WordPackage>;
	selectPackageForUser(userId: string, packageId: string, collectionId: string): Promise<void>;
	hasUserSelectedPackage(userId: string, packageId: string): Promise<boolean>;
	getPackageStats(): Promise<WordPackageStats>;
	searchPackages(query: string, limit?: number): Promise<WordPackageWithStats[]>;
	getPackagesByCategory(category: string, limit?: number): Promise<WordPackageWithStats[]>;
}

export class WordPackageServiceImpl implements WordPackageService {
	constructor(
		private readonly getWordPackageRepository: () => WordPackageRepository,
		private readonly getCollectionService: () => CollectionService,
		private readonly getWordService: () => WordService
	) {}

	async getAvailablePackages(
		userId: string,
		filter: WordPackageFilter = {},
		limit = 20
	): Promise<WordPackageWithStats[]> {
		// Always exclude packages already selected by user
		const filterWithExclusion = {
			...filter,
			excludeUserPackages: true,
		};

		const packages = await this.getWordPackageRepository().findAvailablePackages(
			userId,
			filterWithExclusion,
			limit
		);

		return packages;
	}

	async getUserPackages(
		userId: string,
		limit = 50,
		source_language?: Language,
		target_language?: Language
	): Promise<any[]> {
		const userPackages = await this.getWordPackageRepository().findUserPackages(
			userId,
			limit,
			source_language,
			target_language
		);
		return userPackages;
	}

	async getPackageById(id: string): Promise<WordPackage | null> {
		const wordPackage = await this.getWordPackageRepository().findPackageById(id);
		return wordPackage;
	}

	async createPackage(data: CreateWordPackageInput): Promise<WordPackage> {
		const { words, ...packageData } = data;

		// Create the package
		const wordPackage = await this.getWordPackageRepository().createPackage({
			...packageData,
			word_count: words.length,
		});

		// Add words to the package
		if (words.length > 0) {
			await this.getWordPackageRepository().addWordsToPackage(wordPackage.id, words);
		}

		// Return the package with words
		const createdPackage = await this.getWordPackageRepository().findPackageById(
			wordPackage.id
		);
		return createdPackage!;
	}

	async selectPackageForUser(
		userId: string,
		packageId: string,
		collectionId: string
	): Promise<void> {
		// Check if user already selected this package
		const alreadySelected = await this.getWordPackageRepository().hasUserSelectedPackage(
			userId,
			packageId
		);

		if (alreadySelected) {
			throw new Error('User has already selected this word package');
		}

		// Get the package with words
		const wordPackage = await this.getWordPackageRepository().findPackageById(packageId);
		if (!wordPackage) {
			throw new Error('Word package not found');
		}

		// Get collection service
		const collectionService = this.getCollectionService();
		const wordService = this.getWordService();

		// Create words from package terms and add to collection
		const wordsToAdd = [];
		for (const packageWord of wordPackage.words) {
			try {
				// Try to find existing word first
				const existingWords = await wordService.getWordsByTerms(
					[packageWord.term],
					packageWord.language
				);

				if (existingWords.length > 0) {
					// Use existing word
					wordsToAdd.push(existingWords[0]);
				} else {
					// Create new word with basic structure
					const newWord = await wordService.createWordWithRandomWordDetail({
						term: packageWord.term,
						language: packageWord.language,
						definitions: [
							{
								pos: PartsOfSpeech.NOUN, // Default to NOUN
								ipa: '',
								explains: [],
								examples: [],
							},
						],
					});
					wordsToAdd.push(newWord);
				}
			} catch (error) {
				console.error(`Failed to create word "${packageWord.term}":`, error);
				// Continue with other words
			}
		}

		// Add words to collection
		if (wordsToAdd.length > 0) {
			await collectionService.addWordsToCollection(
				userId,
				collectionId,
				wordsToAdd.map((word) => word.id)
			);
		}

		// Record that user selected this package
		await this.getWordPackageRepository().selectPackageForUser(userId, packageId);
	}

	async hasUserSelectedPackage(userId: string, packageId: string): Promise<boolean> {
		return this.getWordPackageRepository().hasUserSelectedPackage(userId, packageId);
	}

	async getPackageStats(): Promise<WordPackageStats> {
		return this.getWordPackageRepository().getPackageStats();
	}

	async searchPackages(query: string, limit = 20): Promise<WordPackageWithStats[]> {
		return this.getWordPackageRepository().searchPackages(query, limit);
	}

	async getPackagesByCategory(category: string, limit = 20): Promise<WordPackageWithStats[]> {
		return this.getWordPackageRepository().findPackagesByCategory(category, limit);
	}
}
