'use client';

import { UniversalWordCard } from '@/components';
import { WordDetail } from '@/models';
import { Language } from '@prisma/client';
import { memo } from 'react';

interface SelectableWordCardProps {
	word: WordDetail;
	onDeleteWord?: () => void;
	isDeleting?: boolean;
	className?: string;
	isReviewMode?: boolean;
	showSourceLanguage?: boolean;
	onToggleTargetLanguage?: () => void;
	defaultExpanded?: boolean;
	sourceLanguage: Language;
	targetLanguage: Language;
	// Selection props
	isSelected?: boolean;
	onSelectionChange?: (wordId: string, selected: boolean) => void;
	selectionMode?: boolean;
	// WordNet search functionality
	onSearchWordNetTerm?: (term: string) => void;
}

function SelectableWordCardComponent({
	word,
	onDeleteWord,
	isDeleting,
	className,
	isReviewMode = false,
	showSourceLanguage = true,
	onToggleTargetLanguage,
	defaultExpanded,
	sourceLanguage,
	targetLanguage,
	isSelected = false,
	onSelectionChange,
	selectionMode = false,
	onSearchWordNetTerm,
}: SelectableWordCardProps) {
	return (
		<UniversalWordCard
			word={word}
			mode="my-words"
			layout="card"
			sourceLanguage={sourceLanguage}
			targetLanguage={targetLanguage}
			onDeleteWord={onDeleteWord}
			isDeleting={isDeleting}
			isReviewMode={isReviewMode}
			showSourceLanguage={showSourceLanguage}
			onToggleTargetLanguage={onToggleTargetLanguage}
			defaultExpanded={defaultExpanded}
			selectionMode={selectionMode}
			isSelected={isSelected}
			onSelectionChange={onSelectionChange}
			enableLanguageToggle={true}
			defaultShowTargetLanguage={true}
			onSearchWordNetTerm={onSearchWordNetTerm}
			className={className}
		/>
	);
}

// Memoization comparison function
const arePropsEqual = (
	prevProps: SelectableWordCardProps,
	nextProps: SelectableWordCardProps
): boolean => {
	return (
		prevProps.word.id === nextProps.word.id &&
		prevProps.word.term === nextProps.word.term &&
		prevProps.word.updated_at === nextProps.word.updated_at &&
		prevProps.isDeleting === nextProps.isDeleting &&
		prevProps.isReviewMode === nextProps.isReviewMode &&
		prevProps.showSourceLanguage === nextProps.showSourceLanguage &&
		prevProps.defaultExpanded === nextProps.defaultExpanded &&
		prevProps.sourceLanguage === nextProps.sourceLanguage &&
		prevProps.targetLanguage === nextProps.targetLanguage &&
		prevProps.isSelected === nextProps.isSelected &&
		prevProps.selectionMode === nextProps.selectionMode &&
		prevProps.onDeleteWord === nextProps.onDeleteWord &&
		prevProps.onToggleTargetLanguage === nextProps.onToggleTargetLanguage &&
		prevProps.onSelectionChange === nextProps.onSelectionChange &&
		prevProps.onSearchWordNetTerm === nextProps.onSearchWordNetTerm
	);
};

export const SelectableWordCard = memo(SelectableWordCardComponent, arePropsEqual);
