import { useCallback, useReducer, useRef } from 'react';
import { Language } from '@prisma/client';
import { RandomWord, WordDetail } from '@/models';
import {
	getVocabularyGenerationService,
	GenerateWordsParams,
} from '@/backend/services/vocabulary-generation.service';
import { VOCABULARY_GENERATION_CONFIG } from '@/constants/vocabulary-generation';
import { VocabularyGenerationError } from '@/lib/vocabulary-errors';

export interface WordLoadingState {
	adding: boolean;
	gettingDetail: boolean;
	generatingExamples: boolean;
}

interface VocabularyGenerationState {
	generatedWords: RandomWord[];
	detailedWords: Record<string, WordDetail>;
	addedWords: Set<string>;
	wordLoadingStates: Record<string, WordLoadingState>;
	isGenerating: boolean;
	isLoadingMore: boolean;
	currentOffset: number;
	currentKeywords: string[];
	sessionAddedCount: number;
	error: string | null;
}

type VocabularyGenerationAction =
	| { type: 'SET_GENERATING'; payload: boolean }
	| { type: 'SET_LOADING_MORE'; payload: boolean }
	| { type: 'SET_GENERATED_WORDS'; payload: RandomWord[] }
	| { type: 'APPEND_GENERATED_WORDS'; payload: RandomWord[] }
	| { type: 'SET_DETAILED_WORD'; payload: { term: string; detail: WordDetail } }
	| {
			type: 'SET_WORD_LOADING_STATE';
			payload: { term: string; state: Partial<WordLoadingState> };
	  }
	| { type: 'CLEAR_WORD_LOADING_STATE'; payload: string }
	| { type: 'ADD_WORD'; payload: string }
	| { type: 'REMOVE_WORD'; payload: string }
	| { type: 'SET_CURRENT_KEYWORDS'; payload: string[] }
	| { type: 'SET_CURRENT_OFFSET'; payload: number }
	| { type: 'INCREMENT_SESSION_COUNT' }
	| { type: 'SET_ERROR'; payload: string | null }
	| { type: 'RESET_STATE' };

const initialState: VocabularyGenerationState = {
	generatedWords: [],
	detailedWords: {},
	addedWords: new Set(),
	wordLoadingStates: {},
	isGenerating: false,
	isLoadingMore: false,
	currentOffset: 0,
	currentKeywords: [],
	sessionAddedCount: 0,
	error: null,
};

function vocabularyGenerationReducer(
	state: VocabularyGenerationState,
	action: VocabularyGenerationAction
): VocabularyGenerationState {
	switch (action.type) {
		case 'SET_GENERATING':
			return { ...state, isGenerating: action.payload, error: null };

		case 'SET_LOADING_MORE':
			return { ...state, isLoadingMore: action.payload, error: null };

		case 'SET_GENERATED_WORDS':
			return {
				...state,
				generatedWords: action.payload,
				currentOffset: action.payload.length,
			};

		case 'APPEND_GENERATED_WORDS':
			return {
				...state,
				generatedWords: [...state.generatedWords, ...action.payload],
				currentOffset: state.currentOffset + action.payload.length,
			};

		case 'SET_DETAILED_WORD':
			return {
				...state,
				detailedWords: {
					...state.detailedWords,
					[action.payload.term]: action.payload.detail,
				},
			};

		case 'SET_WORD_LOADING_STATE': {
			const { term, state: loadingState } = action.payload;
			const currentState = state.wordLoadingStates[term] || {
				adding: false,
				gettingDetail: false,
				generatingExamples: false,
			};
			return {
				...state,
				wordLoadingStates: {
					...state.wordLoadingStates,
					[term]: {
						...currentState,
						...loadingState,
					},
				},
			};
		}

		case 'CLEAR_WORD_LOADING_STATE': {
			const { [action.payload]: _, ...rest } = state.wordLoadingStates;
			return { ...state, wordLoadingStates: rest };
		}

		case 'ADD_WORD': {
			const newAddedWords = new Set(state.addedWords);
			newAddedWords.add(action.payload);
			return { ...state, addedWords: newAddedWords };
		}

		case 'REMOVE_WORD': {
			const newAddedWords = new Set(state.addedWords);
			newAddedWords.delete(action.payload);
			return { ...state, addedWords: newAddedWords };
		}

		case 'SET_CURRENT_KEYWORDS':
			return { ...state, currentKeywords: action.payload };

		case 'SET_CURRENT_OFFSET':
			return { ...state, currentOffset: action.payload };

		case 'INCREMENT_SESSION_COUNT':
			return { ...state, sessionAddedCount: state.sessionAddedCount + 1 };

		case 'SET_ERROR':
			return { ...state, error: action.payload };

		case 'RESET_STATE':
			return initialState;

		default:
			return state;
	}
}

export function useVocabularyGeneration() {
	const [state, dispatch] = useReducer(vocabularyGenerationReducer, initialState);
	const serviceRef = useRef(getVocabularyGenerationService());

	const generateWords = useCallback(
		async (
			collectionId: string,
			keywords: string[],
			sourceLanguage: Language,
			targetLanguage: Language
		) => {
			if (state.isGenerating) return;

			dispatch({ type: 'SET_GENERATING', payload: true });

			try {
				const params: GenerateWordsParams = {
					collectionId,
					keywords,
					maxTerms: VOCABULARY_GENERATION_CONFIG.WORDS_PER_BATCH,
					excludeTerms: [],
					sourceLanguage,
					targetLanguage,
					offset: VOCABULARY_GENERATION_CONFIG.INITIAL_OFFSET,
				};

				const words = await serviceRef.current.generateWords(params);

				dispatch({ type: 'SET_GENERATED_WORDS', payload: words });
				dispatch({ type: 'SET_CURRENT_KEYWORDS', payload: keywords });
			} catch (error) {
				const errorMessage =
					error instanceof VocabularyGenerationError
						? error.message
						: 'Failed to generate words';
				dispatch({ type: 'SET_ERROR', payload: errorMessage });
				throw error;
			} finally {
				dispatch({ type: 'SET_GENERATING', payload: false });
			}
		},
		[state.isGenerating]
	);

	const loadMoreWords = useCallback(
		async (collectionId: string, sourceLanguage: Language, targetLanguage: Language) => {
			if (state.isLoadingMore || state.currentKeywords.length === 0) return;

			dispatch({ type: 'SET_LOADING_MORE', payload: true });

			try {
				const excludeTerms = state.generatedWords.map((word) => word.term);
				const params: GenerateWordsParams = {
					collectionId,
					keywords: state.currentKeywords,
					maxTerms: VOCABULARY_GENERATION_CONFIG.WORDS_PER_BATCH,
					excludeTerms,
					sourceLanguage,
					targetLanguage,
					offset: state.currentOffset,
				};

				const newWords = await serviceRef.current.loadMoreWords(params);
				dispatch({ type: 'APPEND_GENERATED_WORDS', payload: newWords });
			} catch (error) {
				const errorMessage =
					error instanceof VocabularyGenerationError
						? error.message
						: 'Failed to load more words';
				dispatch({ type: 'SET_ERROR', payload: errorMessage });
				throw error;
			} finally {
				dispatch({ type: 'SET_LOADING_MORE', payload: false });
			}
		},
		[state.isLoadingMore, state.currentKeywords, state.generatedWords, state.currentOffset]
	);

	const addWordToCollection = useCallback(
		async (collectionId: string, term: string, language: Language) => {
			const currentLoadingState = state.wordLoadingStates[term];
			if (currentLoadingState?.adding) return;

			dispatch({
				type: 'SET_WORD_LOADING_STATE',
				payload: { term, state: { adding: true } },
			});

			try {
				const result = await serviceRef.current.addWordToCollection(
					collectionId,
					term,
					language
				);

				dispatch({ type: 'ADD_WORD', payload: term });
				dispatch({ type: 'INCREMENT_SESSION_COUNT' });
				dispatch({ type: 'CLEAR_WORD_LOADING_STATE', payload: term });

				return result;
			} catch (error) {
				dispatch({
					type: 'SET_WORD_LOADING_STATE',
					payload: { term, state: { adding: false } },
				});
				throw error;
			}
		},
		[state.wordLoadingStates]
	);

	const removeWordFromCollection = useCallback(
		async (collectionId: string, wordIds: string[], term: string) => {
			await serviceRef.current.removeWordsFromCollection(collectionId, wordIds);
			dispatch({ type: 'REMOVE_WORD', payload: term });
		},
		[]
	);

	const setWordDetail = useCallback((term: string, detail: WordDetail) => {
		dispatch({ type: 'SET_DETAILED_WORD', payload: { term, detail } });
	}, []);

	const setWordLoadingState = useCallback(
		(term: string, loadingState: Partial<WordLoadingState>) => {
			dispatch({ type: 'SET_WORD_LOADING_STATE', payload: { term, state: loadingState } });
		},
		[]
	);

	const getWordLoadingState = useCallback(
		(term: string): WordLoadingState => {
			return (
				state.wordLoadingStates[term] || {
					adding: false,
					gettingDetail: false,
					generatingExamples: false,
				}
			);
		},
		[state.wordLoadingStates]
	);

	const resetState = useCallback(() => {
		dispatch({ type: 'RESET_STATE' });
	}, []);

	return {
		// State
		generatedWords: state.generatedWords,
		detailedWords: state.detailedWords,
		addedWords: state.addedWords,
		isGenerating: state.isGenerating,
		isLoadingMore: state.isLoadingMore,
		sessionAddedCount: state.sessionAddedCount,
		error: state.error,

		// Actions
		generateWords,
		loadMoreWords,
		addWordToCollection,
		removeWordFromCollection,
		setWordDetail,
		setWordLoadingState,
		getWordLoadingState,
		resetState,
	};
}
