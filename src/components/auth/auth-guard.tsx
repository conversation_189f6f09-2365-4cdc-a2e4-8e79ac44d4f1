'use client';

import { LoadingSpinner } from '@/components/ui';
import { useAuth } from '@/contexts/auth-context';
import { useRouter, usePathname } from 'next/navigation';
import { useEffect, useRef, useState } from 'react';

interface AuthGuardProps {
	children: React.ReactNode;
	fallbackPath?: string;
}

// Routes that don't require authentication
const ALLOWED_ROUTES = ['/login', '/register', '/forgot-password', '/reset-password'] as const;

// Helper function to check if current route is allowed without authentication
const isAllowedRoute = (pathname: string): boolean => {
	return ALLOWED_ROUTES.includes(pathname as (typeof ALLOWED_ROUTES)[number]);
};

export function AuthGuard({ children, fallbackPath = '/login' }: AuthGuardProps) {
	const { user, isLoading, getUser } = useAuth();
	const router = useRouter();
	const pathname = usePathname();
	const [isInitialized, setIsInitialized] = useState(false);
	const hasAttemptedAuth = useRef(false);

	useEffect(() => {
		// Skip authentication checking if current route is in allowed routes
		if (isAllowedRoute(pathname)) {
			setIsInitialized(true);
			return;
		}

		const initAuth = async () => {
			// Only attempt auth once and when not already loading
			if (!hasAttemptedAuth.current && !user && !isLoading) {
				hasAttemptedAuth.current = true;
				await getUser();
			}
			setIsInitialized(true);
		};

		initAuth();
	}, [user, isLoading, getUser, pathname]);

	useEffect(() => {
		// Skip redirect if current route is in allowed routes
		if (isAllowedRoute(pathname)) {
			return;
		}

		if (isInitialized && !isLoading && !user) {
			router.push(fallbackPath);
		}
	}, [isInitialized, isLoading, user, router, fallbackPath, pathname]);

	// Skip authentication checking if current route is in allowed routes
	if (isAllowedRoute(pathname)) {
		return <>{children}</>;
	}

	if (!isInitialized || isLoading || !user) {
		return (
			<div className="min-h-screen flex items-center justify-center">
				<LoadingSpinner size="lg" />
			</div>
		);
	}

	return <>{children}</>;
}
