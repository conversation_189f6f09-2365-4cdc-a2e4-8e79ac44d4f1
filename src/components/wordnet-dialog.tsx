'use client';

import { Dialog, DialogContent, DialogHeader, DialogTitle, Translate } from '@/components/ui';
import { WordNetInfo } from '@/components/wordnet';
import { WordNetData } from '@/models';
import React, { memo } from 'react';

interface WordNetDialogProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	wordNetData: WordNetData;
	wordTerm: string; // For dialog title
	onSearchTerm?: (term: string) => void;
	searchLoading?: Record<string, boolean>;
}

function WordNetDialogComponent({
	open,
	onOpenChange,
	wordNetData,
	wordTerm,
	onSearchTerm,
	searchLoading = {},
}: WordNetDialogProps) {
	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogContent className="max-w-4xl max-h-[80vh] overflow-hidden flex flex-col">
				<DialogHeader>
					<DialogTitle className="flex items-center gap-2">
						<Translate text="wordnet.title" />
						<span className="text-primary font-bold">&quot;{wordTerm}&quot;</span>
					</DialogTitle>
				</DialogHeader>

				<div className="flex-1 overflow-y-auto pr-2">
					<WordNetInfo
						wordNetData={wordNetData}
						term={wordTerm}
						onSearchTerm={onSearchTerm}
						searchLoading={searchLoading}
						className="border-0 shadow-none bg-transparent p-0"
					/>
				</div>
			</DialogContent>
		</Dialog>
	);
}

export const WordNetDialog = memo(WordNetDialogComponent);
