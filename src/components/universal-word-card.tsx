'use client';

import {
	<PERSON><PERSON>,
	<PERSON><PERSON>,
	<PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	Translate,
} from '@/components/ui';
import { ExamplesDialog, WordNetDialog } from '@/components';
import { WordNetSummary } from '@/components/wordnet';
import { cn, getTranslationKeyOfLanguage } from '@/lib';
import { RandomWord, WordDetail, WordNetData } from '@/models';
import { Language } from '@prisma/client';
import { Volume2, ChevronUp, ChevronDown, Plus, Trash2, Undo2, Languages } from 'lucide-react';
import { memo, useCallback, useState, useEffect } from 'react';
import { useSpeechSynthesis } from '@/hooks/use-speech-synthesis';
import { useIntersectionObserver } from '@/hooks/use-intersection-observer';

// Union type for word data
type WordData = WordDetail | (RandomWord & { details?: WordDetail });

interface UniversalWordCardProps {
	// Core word data
	word: WordData;
	sourceLanguage: Language;
	targetLanguage: Language;

	// Display modes
	mode?: 'generate' | 'my-words' | 'review' | 'tiktok';

	// Layout options
	layout?: 'card' | 'fullscreen' | 'compact';
	defaultExpanded?: boolean;
	className?: string;

	// Review mode specific
	isReviewMode?: boolean;
	showSourceLanguage?: boolean;
	onToggleTargetLanguage?: () => void;
	onWordViewed?: (wordId: string) => void;
	isActive?: boolean;

	// Language display toggle (for all modes)
	enableLanguageToggle?: boolean;
	defaultShowTargetLanguage?: boolean;

	// Selection mode (my words)
	selectionMode?: boolean;
	isSelected?: boolean;
	onSelectionChange?: (wordId: string, selected: boolean) => void;

	// Actions
	onAddToCollection?: () => void;
	isAddingToCollection?: boolean;
	onUndoWordAddition?: () => void;
	onDeleteWord?: () => void;
	isDeleting?: boolean;
	onGenerateExamples?: (word: RandomWord) => Promise<void>;
	generatingExamples?: boolean;
	onGetDetails?: () => void;
	isGettingDetails?: boolean;

	// WordNet functionality
	onSearchWordNetTerm?: (term: string) => void;
	wordNetSearchLoading?: Record<string, boolean>;
}

function UniversalWordCardComponent({
	word,
	sourceLanguage,
	targetLanguage,
	mode = 'my-words',
	layout = 'card',
	defaultExpanded,
	className = '',
	isReviewMode = false,
	showSourceLanguage = true,
	onToggleTargetLanguage,
	onWordViewed,
	isActive = false,
	selectionMode = false,
	isSelected = false,
	onSelectionChange,
	onAddToCollection,
	isAddingToCollection = false,
	onUndoWordAddition,
	onDeleteWord,
	isDeleting = false,
	onGenerateExamples,
	generatingExamples = false,
	onGetDetails,
	isGettingDetails = false,
	onSearchWordNetTerm,
	wordNetSearchLoading = {},
	enableLanguageToggle = false,
	defaultShowTargetLanguage = true,
}: UniversalWordCardProps) {
	const { speak, isSupported } = useSpeechSynthesis();
	const [examplesDialogOpen, setExamplesDialogOpen] = useState(false);
	const [wordNetDialogOpen, setWordNetDialogOpen] = useState(false);
	const [showTargetLanguage, setShowTargetLanguage] = useState(defaultShowTargetLanguage);

	// Determine initial expanded state based on mode
	const getInitialExpanded = () => {
		if (defaultExpanded !== undefined) return defaultExpanded;
		if (mode === 'tiktok' || mode === 'review') return false;
		if (mode === 'generate') return true;
		return false;
	};

	const [isExpanded, setIsExpanded] = useState(getInitialExpanded());
	const [hasBeenViewed, setHasBeenViewed] = useState(false);

	// Intersection observer for TikTok mode
	const { elementRef, hasTriggered } = useIntersectionObserver({
		threshold: 0.6,
		triggerOnce: true,
	});

	// Handle word viewed for review modes
	useEffect(() => {
		if (
			hasTriggered &&
			!hasBeenViewed &&
			onWordViewed &&
			(mode === 'tiktok' || mode === 'review')
		) {
			setHasBeenViewed(true);
			const wordId = 'id' in word ? word.id : word.term;
			onWordViewed(wordId);
		}
	}, [hasTriggered, hasBeenViewed, onWordViewed, word, mode]);

	// Extract word data based on type
	const getWordData = () => {
		if ('definitions' in word) {
			// WordDetail
			return {
				id: word.id,
				term: word.term,
				definitions: word.definitions,
				WordNetData: word.WordNetData,
				partOfSpeech: word.definitions?.flatMap((d) => d.pos) || [],
				meaning: null,
			};
		} else {
			// RandomWord
			return {
				id: word.details?.id || word.term,
				term: word.term,
				definitions: word.details?.definitions || [],
				WordNetData: word.details?.WordNetData || word.wordnet_data,
				partOfSpeech: word.partOfSpeech || [],
				meaning: word.meaning || [],
			};
		}
	};

	const wordData = getWordData();

	const handlePlayAudio = useCallback(
		(text: string, language: Language) => {
			if (isSupported) {
				speak(text, language);
			}
		},
		[speak, isSupported]
	);

	const handleToggleExpand = useCallback(() => {
		setIsExpanded(!isExpanded);
	}, [isExpanded]);

	const handleSelectionChange = useCallback(
		(checked: boolean) => {
			if (onSelectionChange && 'id' in word) {
				onSelectionChange(word.id, checked);
			}
		},
		[word, onSelectionChange]
	);

	const handleClick = useCallback(
		(event: React.MouseEvent) => {
			// Handle selection mode clicks
			if (selectionMode) {
				const target = event.target as HTMLElement;
				const isInteractiveElement =
					target.tagName === 'BUTTON' ||
					target.closest('button') ||
					target.tagName === 'A' ||
					target.closest('a') ||
					target.tagName === 'INPUT' ||
					target.closest('input') ||
					target.hasAttribute('onclick') ||
					target.getAttribute('role') === 'button' ||
					target.closest('[role="button"]');

				if (!isInteractiveElement) {
					handleSelectionChange(!isSelected);
				}
			}
		},
		[selectionMode, isSelected, handleSelectionChange]
	);

	// Determine which language to show based on toggle state and mode
	const getDisplayLanguage = () => {
		if (enableLanguageToggle) {
			return showTargetLanguage ? targetLanguage : sourceLanguage;
		}
		// Default behavior for review mode
		if (isReviewMode) {
			return showSourceLanguage ? sourceLanguage : targetLanguage;
		}
		// Default to target language for other modes
		return targetLanguage;
	};

	const getSecondaryLanguage = () => {
		if (enableLanguageToggle) {
			return showTargetLanguage ? sourceLanguage : targetLanguage;
		}
		// Default behavior for review mode
		if (isReviewMode) {
			return showSourceLanguage ? targetLanguage : sourceLanguage;
		}
		// Default to source language for other modes
		return sourceLanguage;
	};

	// Check if we should show both languages or just one
	const shouldShowBothLanguages = () => {
		if (enableLanguageToggle) {
			return false; // Only show one language when toggle is enabled
		}
		// Default behavior for review mode and other modes
		return true;
	};

	// Render language toggle button
	const renderLanguageToggle = () => {
		if (!enableLanguageToggle) return null;

		return (
			<div className="flex items-center gap-2 mb-3">
				<Button
					variant="outline"
					size="sm"
					onClick={() => setShowTargetLanguage(!showTargetLanguage)}
					className="flex items-center gap-2 text-xs"
				>
					<Languages className="h-3 w-3" />
					{showTargetLanguage ? (
						<Translate text={getTranslationKeyOfLanguage(targetLanguage)} />
					) : (
						<Translate text={getTranslationKeyOfLanguage(sourceLanguage)} />
					)}
				</Button>
			</div>
		);
	};

	// Render header section
	const renderHeader = () => (
		<div className="flex-grow">
			<div className="flex items-center gap-3 mb-2">
				<h3
					className={cn(
						'font-bold tracking-tight text-primary drop-shadow-sm',
						layout === 'fullscreen' ? 'text-3xl' : 'text-2xl md:text-3xl'
					)}
				>
					{wordData.term}
				</h3>
				{isSupported && (
					<Button
						variant="ghost"
						size="sm"
						onClick={() => handlePlayAudio(wordData.term, sourceLanguage)}
						className="h-8 w-8 p-0 hover:bg-primary/10 transition-colors"
						title={`Pronounce "${wordData.term}"`}
					>
						<Volume2 className="h-4 w-4 text-primary" />
					</Button>
				)}
			</div>

			{/* Part of speech badges */}
			{wordData.partOfSpeech && wordData.partOfSpeech.length > 0 && (
				<div className="flex flex-wrap gap-1 mb-2">
					{wordData.partOfSpeech.map((pos, index) => (
						<Badge key={index} variant="secondary" className="text-xs font-medium">
							{pos}
						</Badge>
					))}
				</div>
			)}

			{/* WordNet Summary */}
			{wordData.WordNetData && (
				<div className="mt-2">
					<WordNetSummary wordNetData={wordData.WordNetData} />
				</div>
			)}
		</div>
	);

	// Render action buttons
	const renderActionButtons = () => {
		if (mode === 'generate') {
			return (
				<div className="flex flex-col items-end space-y-1.5 flex-shrink-0">
					{/* Get Details button - show when word doesn't have detailed definitions */}
					{onGetDetails && wordData.definitions.length === 0 && (
						<Button
							variant="secondary"
							size="sm"
							onClick={onGetDetails}
							disabled={isGettingDetails}
							className="flex items-center gap-2"
						>
							{isGettingDetails ? (
								<>
									<div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
									<Translate text="words.getting_details" />
								</>
							) : (
								<>
									<Translate text="words.get_details" />
								</>
							)}
						</Button>
					)}
					{onAddToCollection && (
						<Button
							variant="default"
							size="sm"
							onClick={onAddToCollection}
							disabled={isAddingToCollection}
							className="flex items-center gap-2"
						>
							{isAddingToCollection ? (
								<>
									<div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
									<Translate text="words.adding" />
								</>
							) : (
								<>
									<Plus className="w-4 h-4" />
									<Translate text="words.add_to_collection" />
								</>
							)}
						</Button>
					)}
					{onUndoWordAddition && (
						<Button
							variant="outline"
							size="sm"
							onClick={onUndoWordAddition}
							className="flex items-center gap-2"
						>
							<Undo2 className="w-4 h-4" />
							<Translate text="words.undo" />
						</Button>
					)}
				</div>
			);
		}

		if (mode === 'my-words') {
			return (
				<div className="flex flex-col items-end space-y-1.5 flex-shrink-0">
					<Button
						variant="ghost"
						size="icon"
						onClick={handleToggleExpand}
						aria-label={isExpanded ? 'Collapse' : 'Expand'}
					>
						{isExpanded ? <ChevronUp size={20} /> : <ChevronDown size={20} />}
					</Button>
					{onDeleteWord && (
						<Button
							variant="ghost"
							size="sm"
							onClick={onDeleteWord}
							disabled={isDeleting}
							className="text-destructive hover:text-destructive"
						>
							{isDeleting ? (
								<div className="w-4 h-4 border-2 border-destructive border-t-transparent rounded-full animate-spin" />
							) : (
								<Trash2 className="w-4 h-4" />
							)}
						</Button>
					)}
				</div>
			);
		}

		if (mode === 'review' || mode === 'tiktok') {
			return (
				<div className="flex flex-col items-end space-y-1.5 flex-shrink-0">
					<Button
						variant="ghost"
						size="icon"
						onClick={handleToggleExpand}
						aria-label={isExpanded ? 'Collapse' : 'Expand'}
					>
						{isExpanded ? <ChevronUp size={20} /> : <ChevronDown size={20} />}
					</Button>
				</div>
			);
		}

		return null;
	};

	// Render basic meaning (for RandomWord without details)
	const renderBasicMeaning = () => {
		if (!wordData.meaning || wordData.meaning.length === 0) return null;

		const primaryLang = getDisplayLanguage();
		const secondaryLang = getSecondaryLanguage();
		const showBothLanguages = shouldShowBothLanguages();

		return (
			<div className="space-y-2">
				{wordData.meaning.map((meaning, index) => (
					<div
						key={index}
						className="p-4 rounded-xl border border-border/70 bg-accent/25 hover:bg-accent/40 transition-colors duration-150 dark:bg-accent/15 dark:hover:bg-accent/30"
					>
						<div className="mb-2 last:mb-0 pl-3 border-l-2 border-primary/30 py-1">
							{/* Primary language */}
							<div className="flex items-start gap-2 mb-1">
								<p className="flex-1 text-sm text-foreground/95">
									{meaning[primaryLang] || (
										<span className="italic opacity-70">
											<Translate text="words.translation_not_provided" />
										</span>
									)}
								</p>
								{isSupported && meaning[primaryLang] && (
									<Button
										variant="ghost"
										size="sm"
										onClick={() =>
											handlePlayAudio(meaning[primaryLang], primaryLang)
										}
										className="h-6 w-6 p-0 hover:bg-primary/10 transition-colors flex-shrink-0"
										title={`Pronounce meaning in ${primaryLang}`}
									>
										<Volume2 className="h-3 w-3 text-primary" />
									</Button>
								)}
							</div>

							{/* Secondary language - show based on mode and toggle */}
							{showBothLanguages && (
								<>
									<div className="flex items-start gap-2">
										<p className="flex-1 text-sm text-foreground/95">
											{meaning[secondaryLang] || (
												<span className="italic opacity-70">
													<Translate text="words.translation_not_provided" />
												</span>
											)}
										</p>
										{isSupported && meaning[secondaryLang] && (
											<Button
												variant="ghost"
												size="sm"
												onClick={() =>
													handlePlayAudio(
														meaning[secondaryLang],
														secondaryLang
													)
												}
												className="h-6 w-6 p-0 hover:bg-primary/10 transition-colors flex-shrink-0"
												title={`Pronounce meaning in ${secondaryLang}`}
											>
												<Volume2 className="h-3 w-3 text-primary" />
											</Button>
										)}
									</div>
								</>
							)}
						</div>
					</div>
				))}
			</div>
		);
	};

	// Render definitions with examples
	const renderDefinitions = () => {
		if (!wordData.definitions || wordData.definitions.length === 0) {
			return (
				<p className="p-4 text-sm text-muted-foreground italic">
					<Translate text="words.no_definitions_available" />
				</p>
			);
		}

		return wordData.definitions.map((definition, index) => (
			<div
				key={index}
				className={cn(
					'p-4 rounded-xl border border-border/70 bg-accent/25 dark:bg-accent/15',
					!selectionMode &&
						'hover:bg-accent/40 dark:hover:bg-accent/30 transition-colors duration-150'
				)}
			>
				{definition.pos && (
					<p className="text-xs font-semibold uppercase tracking-wider text-primary/90 mb-2.5">
						{definition.pos}
					</p>
				)}
				{definition.ipa && (
					<p className="text-sm text-muted-foreground italic mb-2.5">
						IPA: {definition.ipa}
					</p>
				)}

				{/* Explains */}
				{definition.explains && definition.explains.length > 0 && (
					<div className="mb-4">
						<p className="text-sm font-semibold text-muted-foreground mb-1.5">
							<Translate text="words.explains" />:
						</p>
						<div className="space-y-2">
							{definition.explains.map((explain, explainIndex) => {
								const primaryLang = getDisplayLanguage();
								const secondaryLang = getSecondaryLanguage();
								const showBothLanguages = shouldShowBothLanguages();

								return (
									<div
										key={explainIndex}
										className="mb-2 last:mb-0 pl-3 border-l-2 border-secondary/30 py-1"
									>
										{/* Primary language */}
										<div className="flex items-start gap-2 mb-1">
											<p className="flex-1 text-sm text-foreground/95">
												{explain[primaryLang] || (
													<span className="italic opacity-70">
														<Translate text="words.explain_not_provided" />
													</span>
												)}
											</p>
											{isSupported && explain[primaryLang] && (
												<Button
													variant="ghost"
													size="sm"
													onClick={() =>
														handlePlayAudio(
															explain[primaryLang],
															primaryLang
														)
													}
													className="h-6 w-6 p-0 hover:bg-primary/10 transition-colors flex-shrink-0"
													title={`Pronounce explain in ${primaryLang}`}
												>
													<Volume2 className="h-3 w-3 text-primary" />
												</Button>
											)}
										</div>

										{/* Secondary language - show based on toggle */}
										{showBothLanguages && (
											<>
												<div className="flex items-start gap-2">
													<p className="flex-1 text-sm text-foreground/95">
														{explain[secondaryLang] || (
															<span className="italic opacity-70">
																<Translate text="words.explain_not_provided" />
															</span>
														)}
													</p>
													{isSupported && explain[secondaryLang] && (
														<Button
															variant="ghost"
															size="sm"
															onClick={() =>
																handlePlayAudio(
																	explain[secondaryLang],
																	secondaryLang
																)
															}
															className="h-6 w-6 p-0 hover:bg-primary/10 transition-colors flex-shrink-0"
															title={`Pronounce explain in ${secondaryLang}`}
														>
															<Volume2 className="h-3 w-3 text-primary" />
														</Button>
													)}
												</div>
											</>
										)}
									</div>
								);
							})}
						</div>
					</div>
				)}

				{/* Examples section - Show only first example with dialog */}
				{mode === 'generate' && !wordData.definitions.length ? (
					// Generate mode without detailed definitions
					<div>
						<div className="flex items-center justify-between mb-1.5">
							<p className="text-sm font-semibold text-muted-foreground">
								<Translate text="words.examples" />:
							</p>
						</div>
						<p className="text-sm text-muted-foreground italic">
							<Translate text="words.save_word_to_load_examples" />
						</p>
					</div>
				) : definition.examples && definition.examples.length > 0 ? (
					<div>
						<div className="flex items-center justify-between mb-1.5">
							<p className="text-sm font-semibold text-muted-foreground">
								<Translate text="words.examples" />:
							</p>
							{definition.examples.length > 1 && (
								<Button
									variant="ghost"
									size="sm"
									onClick={() => setExamplesDialogOpen(true)}
									className="text-xs text-primary hover:text-primary/80"
								>
									<Translate text="words.view_all_examples" />
								</Button>
							)}
						</div>
						{/* Show only the first example */}
						{definition.examples.slice(0, 1).map((example, exIndex) => {
							const primaryLang = getDisplayLanguage();
							const secondaryLang = getSecondaryLanguage();
							const showBothLanguages = shouldShowBothLanguages();

							return (
								<div
									key={exIndex}
									className="mb-2 last:mb-0 pl-3 border-l-2 border-secondary/30 py-1"
								>
									{/* Primary language */}
									<div className="flex items-start gap-2 mb-1">
										<p className="flex-1 text-sm text-foreground/95">
											{example[primaryLang] || (
												<span className="italic opacity-70">
													<Translate text="words.example_not_provided" />
												</span>
											)}
										</p>
										{isSupported && example[primaryLang] && (
											<Button
												variant="ghost"
												size="sm"
												onClick={() =>
													handlePlayAudio(
														example[primaryLang],
														primaryLang
													)
												}
												className="h-6 w-6 p-0 hover:bg-primary/10 transition-colors flex-shrink-0"
												title={`Pronounce example in ${primaryLang}`}
											>
												<Volume2 className="h-3 w-3 text-primary" />
											</Button>
										)}
									</div>

									{/* Secondary language - show based on toggle */}
									{showBothLanguages && (
										<>
											<div className="flex items-start gap-2">
												<p className="flex-1 text-sm text-foreground/95">
													{example[secondaryLang] || (
														<span className="italic opacity-70">
															<Translate text="words.translation_not_provided" />
														</span>
													)}
												</p>
												{isSupported && example[secondaryLang] && (
													<Button
														variant="ghost"
														size="sm"
														onClick={() =>
															handlePlayAudio(
																example[secondaryLang],
																secondaryLang
															)
														}
														className="h-6 w-6 p-0 hover:bg-primary/10 transition-colors flex-shrink-0"
														title={`Pronounce example in ${secondaryLang}`}
													>
														<Volume2 className="h-3 w-3 text-primary" />
													</Button>
												)}
											</div>
										</>
									)}
								</div>
							);
						})}
					</div>
				) : (
					<div>
						<p className="text-sm font-semibold text-muted-foreground mb-1.5">
							<Translate text="words.examples" />:
						</p>
						<p className="p-2 text-sm text-muted-foreground italic opacity-70">
							<Translate text="words.no_examples_provided" />
						</p>
					</div>
				)}
			</div>
		));
	};

	// Render WordNet button
	const renderWordNetButton = () => {
		if (!wordData.WordNetData) return null;

		return (
			<div className="mt-4">
				<Button
					variant="outline"
					size="sm"
					onClick={() => setWordNetDialogOpen(true)}
					className="w-full flex items-center justify-center gap-2"
				>
					<Translate text="words.view_wordnet_info" />
				</Button>
			</div>
		);
	};

	// Main render logic
	if (layout === 'fullscreen') {
		// TikTok-style fullscreen layout
		return (
			<div
				ref={elementRef}
				className={cn(
					'h-full w-full flex flex-col justify-center items-center p-6 snap-start',
					'bg-gradient-to-br from-background via-background to-accent/20',
					className
				)}
				onClick={handleClick}
			>
				<Card className="w-full max-w-md mx-auto shadow-2xl border-2 border-border/50">
					<CardHeader className="text-center pb-4">{renderHeader()}</CardHeader>

					{isExpanded && (
						<CardContent className="space-y-4">
							{/* Language toggle */}
							{renderLanguageToggle()}

							{/* Show basic meaning if no detailed definitions */}
							{wordData.definitions.length === 0 && renderBasicMeaning()}

							{/* Show detailed definitions */}
							{wordData.definitions.length > 0 && renderDefinitions()}

							{/* WordNet button */}
							{renderWordNetButton()}
						</CardContent>
					)}
				</Card>

				{/* Dialogs */}
				{wordData.definitions.length > 0 &&
					wordData.definitions[0].examples &&
					wordData.definitions[0].examples.length > 0 && (
						<ExamplesDialog
							open={examplesDialogOpen}
							onOpenChange={setExamplesDialogOpen}
							wordId={wordData.id}
							definition={wordData.definitions[0]}
							sourceLanguage={sourceLanguage}
							targetLanguage={targetLanguage}
							wordTerm={wordData.term}
						/>
					)}

				{wordData.WordNetData && (
					<WordNetDialog
						open={wordNetDialogOpen}
						onOpenChange={setWordNetDialogOpen}
						wordNetData={wordData.WordNetData as WordNetData}
						wordTerm={wordData.term}
						onSearchTerm={onSearchWordNetTerm}
					/>
				)}
			</div>
		);
	}

	// Card layout (default)
	const cardContent = (
		<Card
			className={cn(
				'flex flex-col shadow-lg border border-border bg-background transition-shadow duration-200',
				layout === 'compact' ? 'hover:shadow-md' : 'hover:shadow-xl',
				mode === 'generate' && 'break-inside-avoid',
				className
			)}
			data-expanded={isExpanded}
		>
			<CardHeader className="py-4 px-5 border-b border-border bg-gradient-to-r from-primary/5 via-primary/10 to-transparent rounded-t-lg">
				<CardTitle className="flex justify-between items-start gap-3">
					{renderHeader()}
					{renderActionButtons()}
				</CardTitle>
			</CardHeader>

			{(isExpanded || mode === 'generate') && (
				<CardContent className="flex-grow flex flex-col p-5">
					<div className="space-y-4 flex-grow">
						{/* Language toggle */}
						{renderLanguageToggle()}

						{/* Show basic meaning if no detailed definitions */}
						{wordData.definitions.length === 0 && renderBasicMeaning()}

						{/* Show detailed definitions */}
						{wordData.definitions.length > 0 && renderDefinitions()}

						{/* WordNet button */}
						{renderWordNetButton()}
					</div>
				</CardContent>
			)}

			{/* Dialogs */}
			{wordData.definitions.length > 0 &&
				wordData.definitions[0].examples &&
				wordData.definitions[0].examples.length > 0 && (
					<ExamplesDialog
						open={examplesDialogOpen}
						onOpenChange={setExamplesDialogOpen}
						wordId={wordData.id}
						definition={wordData.definitions[0]}
						sourceLanguage={sourceLanguage}
						targetLanguage={targetLanguage}
						wordTerm={wordData.term}
					/>
				)}

			{wordData.WordNetData && (
				<WordNetDialog
					open={wordNetDialogOpen}
					onOpenChange={setWordNetDialogOpen}
					wordNetData={wordData.WordNetData as WordNetData}
					wordTerm={wordData.term}
					onSearchTerm={onSearchWordNetTerm}
				/>
			)}
		</Card>
	);

	// Wrap with selection styling if needed
	if (selectionMode) {
		return (
			<div
				className={cn(
					'cursor-pointer transition-all duration-200',
					isSelected &&
						'ring-2 ring-primary ring-offset-2 ring-offset-background border-2 border-primary rounded-lg'
				)}
				onClick={handleClick}
			>
				{cardContent}
			</div>
		);
	}

	return cardContent;
}

const arePropsEqual = (prevProps: UniversalWordCardProps, nextProps: UniversalWordCardProps) => {
	const prevWord = prevProps.word;
	const nextWord = nextProps.word;

	// Compare word identity
	const prevWordId = 'id' in prevWord ? prevWord.id : prevWord.term;
	const nextWordId = 'id' in nextWord ? nextWord.id : nextWord.term;

	// For RandomWord with details, also compare if details changed
	const prevHasDetails = 'details' in prevWord && prevWord.details;
	const nextHasDetails = 'details' in nextWord && nextWord.details;
	const detailsChanged =
		prevHasDetails !== nextHasDetails ||
		(prevHasDetails &&
			nextHasDetails &&
			'details' in prevWord &&
			'details' in nextWord &&
			prevWord.details?.id !== nextWord.details?.id);

	return (
		prevWordId === nextWordId &&
		!detailsChanged &&
		prevProps.mode === nextProps.mode &&
		prevProps.layout === nextProps.layout &&
		prevProps.sourceLanguage === nextProps.sourceLanguage &&
		prevProps.targetLanguage === nextProps.targetLanguage &&
		prevProps.isReviewMode === nextProps.isReviewMode &&
		prevProps.showSourceLanguage === nextProps.showSourceLanguage &&
		prevProps.selectionMode === nextProps.selectionMode &&
		prevProps.isSelected === nextProps.isSelected &&
		prevProps.isAddingToCollection === nextProps.isAddingToCollection &&
		prevProps.isDeleting === nextProps.isDeleting &&
		prevProps.generatingExamples === nextProps.generatingExamples &&
		prevProps.isGettingDetails === nextProps.isGettingDetails &&
		prevProps.enableLanguageToggle === nextProps.enableLanguageToggle &&
		prevProps.defaultShowTargetLanguage === nextProps.defaultShowTargetLanguage &&
		prevProps.className === nextProps.className
	);
};

export const UniversalWordCard = memo(UniversalWordCardComponent, arePropsEqual);
