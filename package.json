{"name": "app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack -p 6001", "scan": "next dev --turbopack & npx react-scan@latest localhost:3000", "build": "yarn p:m && next build --turbopack", "start": "next start", "lint": "yarn lint:oxlint && yarn lint:eslint && yarn lint:tsc", "lint:tsc": "tsc --noEmit --skipL<PERSON><PERSON><PERSON><PERSON>", "lint:oxlint": "oxlint ./src", "lint:eslint": "eslint ./src --fix", "lint:fix": "yarn lint:oxlint --fix && yarn lint:eslint", "lint:oxlint:fix": "oxlint ./src --fix", "lint:oxlint:quiet": "oxlint ./src --quiet", "lint:check": "yarn lint:oxlint:quiet && yarn lint:eslint", "format": "npx prettier --write \"./src/**/*.{ts,tsx,md,json,js,jsx}\"", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false", "test:unit": "vitest", "test:unit:watch": "vitest --watch", "test:unit:coverage": "vitest --coverage", "test:unit:ui": "vitest --ui", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:debug": "playwright test --debug", "test:e2e:headed": "playwright test --headed", "test:performance": "jest --testPathPattern=performance --runInBand", "test:load": "jest --testPathPattern=load --runInBand", "test:security": "jest --testPathPattern=security --runInBand", "test:integration": "jest --testPathPattern=integration --runInBand", "test:all": "yarn test:unit && yarn test && yarn test:e2e", "test:all:comprehensive": "yarn test:unit && yarn test && yarn test:performance && yarn test:load && yarn test:security && yarn test:e2e", "test:setup": "yarn dup && sleep 5 && yarn p:m && yarn seed:admin:sample", "p:m": "prisma migrate dev", "p:m:r": "prisma migrate reset", "p:s": "prisma studio", "seed:admin": "tsx scripts/seed-admin.ts", "wordnet:full": "tsx scripts/wordnet.ts full", "seed:word-packages": "tsx scripts/seed-word-packages.ts", "seed:cefr": "tsx scripts/seed-cefr.ts", "seed": "yarn seed:admin && yarn seed:word-packages && seed:cefr", "dup": "docker compose up -d", "claude": "claude --permission-mode bypassPermissions"}, "dependencies": {"@google-cloud/firestore": "^7.11.3", "@hookform/resolvers": "^4.1.3", "@inquirer/prompts": "^7.7.1", "@opentelemetry/api": "^1.9.0", "@opentelemetry/exporter-jaeger": "^2.0.1", "@opentelemetry/winston-transport": "^0.14.0", "@prisma/client": "6.10.1", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tooltip": "^1.2.7", "@tailwindcss/postcss": "^4.1.10", "@types/bcryptjs": "^2.4.6", "@types/nprogress": "^0.2.3", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "csv-parse": "^6.1.0", "csv-parser": "^3.2.0", "firebase-admin": "^13.4.0", "framer-motion": "^12.6.2", "i18next": "^23.7.13", "ioredis": "^5.6.1", "jose": "^6.0.10", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.483.0", "natural": "^8.1.0", "next": "^15.4.1", "next-auth": "^4.24.11", "next-i18next": "^15.4.2", "next-themes": "^0.4.6", "node-cache": "^5.1.2", "node-fetch": "^3.3.2", "nprogress": "^0.2.0", "openai": "^5.8.2", "radix-ui": "^1.1.3", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.54.2", "react-i18next": "^13.5.0", "react-window": "^1.8.11", "recharts": "^2.15.3", "sonner": "^2.0.1", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "ts-node": "^10.9.2", "tw-animate-css": "^1.2.4", "unzipper": "^0.12.3", "uuid": "^11.1.0", "wordnet-db": "^3.1.14", "wordpos": "^2.1.0", "zod": "^3.24.2"}, "devDependencies": {"@axe-core/playwright": "^4.10.2", "@eslint/eslintrc": "^3", "@jest/globals": "^30.0.3", "@next/eslint-plugin-next": "15.4.1", "@playwright/test": "^1.53.1", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/identity-obj-proxy": "^3", "@types/ioredis": "^5.0.0", "@types/jsonwebtoken": "^9.0.9", "@types/natural": "^6.0.1", "@types/node": "^20", "@types/react": "19.1.8", "@types/react-dom": "19.1.6", "@types/react-window": "^1.8.8", "@types/supertest": "^6.0.3", "@types/unzipper": "^0.10.11", "@vitejs/plugin-react": "^4.6.0", "dotenv": "^16.4.7", "eslint": "^9.30.0", "eslint-config-next": "^15.4.1", "eslint-config-prettier": "^10.1.1", "eslint-plugin-oxlint": "^1.7.0", "eslint-plugin-react": "^7.37.5", "identity-obj-proxy": "^3.0.0", "jest": "^30.0.3", "jest-environment-jsdom": "^30.0.2", "msw": "^2.10.2", "nock": "^14.0.5", "oxlint": "^1.7.0", "playwright": "^1.53.1", "playwright-core": "^1.53.1", "prisma": "^6.12.0", "react-scan": "^0.3.4", "supertest": "^7.1.1", "tailwindcss": "^4", "ts-jest": "^29.4.0", "tsx": "^4.20.3", "typescript": "^5", "typescript-eslint": "^8.35.1", "undici": "^7.12.0", "vite": "^7.0.0", "vitest": "^3.2.4"}, "packageManager": "yarn@4.9.2", "resolutions": {"@types/react": "19.1.8", "@types/react-dom": "19.1.6"}}