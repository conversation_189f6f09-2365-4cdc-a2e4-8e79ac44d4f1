import '@/app/globals.css';
import { <PERSON><PERSON><PERSON>, ThemeProvider } from '@/components/ui';
import { FloatingUIManager, FloatingUIProvider } from '@/components/floating-ui';

import { SimpleEnhancedFloatingButtons } from '@/components/floating-ui/simple-enhanced-floating-buttons';
import { AuthProvider } from '@/contexts/auth-context';
import { GuidanceProvider } from '@/contexts/guidance-context';
import { LoadingProvider } from '@/contexts/loading-context';
import { SimpleFloatingManager, SimpleFloatingProvider } from '@/contexts/simple-floating-context';
import { ToastProvider } from '@/contexts/toast-context';
import { TranslationProvider } from '@/contexts/translation-context';
import { ErrorManagementProvider } from '@/providers/error-management-provider';
import {
	generateMetadata as generateSeoMetadata,
	generateViewport as generateSeoViewport,
} from '@/lib/seo';
import { Metadata, Viewport } from 'next';
import { <PERSON><PERSON><PERSON>, <PERSON>eist_Mono } from 'next/font/google';
import { Toaster } from 'sonner';
import { AuthGuard } from '@/components/auth';

const geist = Geist({
	subsets: ['latin'],
	variable: '--font-geist',
	preload: true,
});

const geistMono = Geist_Mono({
	subsets: ['latin'],
	variable: '--font-geist-mono',
	preload: true,
});

// Generate dynamic metadata from SEO settings
export async function generateMetadata(): Promise<Metadata> {
	return generateSeoMetadata();
}

// Generate dynamic viewport from SEO settings
export async function generateViewport(): Promise<Viewport> {
	return generateSeoViewport();
}

export default async function RootLayout({
	children,
}: Readonly<{
	children: React.ReactNode;
}>) {
	return (
		<html lang="en" suppressHydrationWarning>
			<head>
				<link rel="manifest" href="/manifest.json" />
			</head>
			<body
				className={`${geist.variable} ${geistMono.variable} min-h-screen flex flex-col`}
				role="application"
				aria-label="Vocab Learning Application"
				aria-live="polite"
				suppressHydrationWarning
			>
				<ErrorManagementProvider
					config={{
						enableErrorReporting: true,
						enableApiInterception: true,
						environment: process.env.NODE_ENV,
						buildVersion: process.env.NEXT_PUBLIC_BUILD_VERSION,
					}}
				>
					<ThemeProvider>
						<TranslationProvider>
							<LoadingProvider>
								<ToastProvider>
									<SimpleFloatingProvider>
										<FloatingUIProvider>
											<GuidanceProvider>
												<AuthProvider>
													<AuthGuard>
														<main className="flex-grow px-2 sm:px-6 lg:px-9 py-2 sm:py-4 lg:py-8">
															<div className="container mx-auto max-w-7xl">
																<div className="w-full">
																	{children}
																</div>
															</div>
														</main>
														<ProgressBar />
														<SimpleFloatingManager />
														<FloatingUIManager />
														<SimpleEnhancedFloatingButtons
															includeGuidance={true}
															includeLoading={true}
															includeBackToTop={true}
														/>
													</AuthGuard>
												</AuthProvider>
											</GuidanceProvider>
										</FloatingUIProvider>
									</SimpleFloatingProvider>
								</ToastProvider>
							</LoadingProvider>
						</TranslationProvider>
					</ThemeProvider>
					<Toaster
						position="bottom-center"
						// richColors
						closeButton={false}
						expand={true}
						visibleToasts={5}
						toastOptions={{
							style: {
								background: 'hsl(var(--background))',
								color: 'hsl(var(--foreground))',
								border: '1px solid hsl(var(--border))',
							},
							className: 'sonner-toast',
						}}
					/>
				</ErrorManagementProvider>
			</body>
		</html>
	);
}
