generator client {
  provider = "prisma-client-js"
  output   = "../node_modules/.prisma/client"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id               String            @id @default(uuid())
  provider         Provider
  provider_id      String
  disabled         <PERSON>ole<PERSON>           @default(false)
  created_at       DateTime          @default(now())
  updated_at       DateTime          @updatedAt
  password_hash    String?
  username         String?           @unique
  role             Role              @default(USER)
  audit_logs_admin AuditLog[]        @relation("AuditLogAdmin")
  audit_logs_user  AuditLog[]        @relation("AuditLogUser")
  collections      Collection[]
  collection_stats CollectionStats[]
  feedbacks        Feedback[]
  keywords         Keyword[]
  last_seen_words  LastSeenWord[]
  UserWordPackage  UserWordPackage[]

  @@unique([provider, provider_id])
}

model Word {
  id            String       @id @default(uuid())
  term          String
  language      Language
  created_at    DateTime     @default(now())
  updated_at    DateTime     @updatedAt
  audio_url     String?
  wordNetDataId String?
  definitions   Definition[]
  WordNetData   WordNetData? @relation(fields: [wordNetDataId], references: [id])

  @@unique([term, language])
  @@index([term])
  @@index([language])
  @@index([term, language])
}

model Definition {
  id       String        @id @default(uuid())
  word_id  String
  pos      PartsOfSpeech
  ipa      String
  images   String[]
  word     Word          @relation(fields: [word_id], references: [id])
  examples Example[]
  explains Explain[]

  @@unique([word_id, pos])
}

model Explain {
  id            String     @id @default(uuid())
  definition_id String
  EN            String
  VI            String
  definition    Definition @relation(fields: [definition_id], references: [id])
}

model Example {
  id            String     @id @default(uuid())
  definition_id String
  EN            String
  VI            String
  created_at    DateTime   @default(now())
  updated_at    DateTime   @updatedAt
  definition    Definition @relation(fields: [definition_id], references: [id])

  @@unique([definition_id, EN, VI])
  @@index([definition_id, created_at])
}

model WordNetData {
  id         String   @id @default(uuid())
  synsets    String[]
  lemma      String?
  hypernyms  String[]
  hyponyms   String[]
  holonyms   String[]
  meronyms   String[]
  created_at DateTime @default(now())
  updated_at DateTime @updatedAt
  pos        String
  term       String
  Word       Word[]

  @@unique([term, pos])
  @@index([term])
  @@index([lemma])
  @@index([pos])
}

model Keyword {
  id      String @id @default(uuid())
  content String
  user_id String
  user    User   @relation(fields: [user_id], references: [id], onDelete: Cascade)

  @@index([user_id])
}

model Collection {
  id                             String            @id @default(uuid())
  name                           String
  user_id                        String
  created_at                     DateTime          @default(now())
  updated_at                     DateTime          @updatedAt
  word_ids                       String[]
  keyword_ids                    String[]
  paragraph_ids                  String[]
  enable_learn_word_notification Boolean           @default(false)
  source_language                Language          @default(VI)
  target_language                Language          @default(EN)
  user                           User              @relation(fields: [user_id], references: [id], onDelete: Cascade)
  collection_stats               CollectionStats[]

  @@index([user_id])
}

model LastSeenWord {
  id           String   @id @default(uuid())
  user_id      String
  word_id      String
  last_seen_at DateTime @default(now())
  review_count Int      @default(0)
  user         User     @relation(fields: [user_id], references: [id], onDelete: Cascade)

  @@unique([user_id, word_id])
  @@index([user_id])
}

model Paragraph {
  id                        String                   @id @default(uuid())
  content                   String
  difficulty                Difficulty
  language                  Language
  length                    Length
  multiple_choice_exercises MultipleChoiceExercise[]

  @@index([language])
  @@index([difficulty])
}

model MultipleChoiceExercise {
  id           String    @id @default(uuid())
  question     String
  options      String[]
  answer       Int
  explanation  String?
  paragraph_id String
  paragraph    Paragraph @relation(fields: [paragraph_id], references: [id])

  @@index([paragraph_id])
}

model Feedback {
  id         String   @id @default(uuid())
  message    String
  created_at DateTime @default(now())
  user_id    String
  status     String   @default("pending")
  user       User     @relation(fields: [user_id], references: [id], onDelete: Cascade)

  @@index([user_id])
}

model CollectionStats {
  id                             String     @id @default(uuid())
  collection_id                  String
  user_id                        String
  date                           DateTime   @db.Date
  words_reviewed_count           Int        @default(0)
  qa_practice_submissions        Int        @default(0)
  paragraph_practice_submissions Int        @default(0)
  created_at                     DateTime   @default(now())
  updated_at                     DateTime   @updatedAt
  collection                     Collection @relation(fields: [collection_id], references: [id], onDelete: Cascade)
  user                           User       @relation(fields: [user_id], references: [id], onDelete: Cascade)

  @@unique([collection_id, user_id, date])
  @@index([collection_id])
  @@index([user_id])
  @@index([date])
}

model AuditLog {
  id          String   @id @default(uuid())
  action      String
  resource    String
  resource_id String?
  user_id     String?
  admin_id    String?
  details     Json     @default("{}")
  ip_address  String?
  user_agent  String?
  timestamp   DateTime @default(now())
  admin       User?    @relation("AuditLogAdmin", fields: [admin_id], references: [id])
  user        User?    @relation("AuditLogUser", fields: [user_id], references: [id])

  @@index([action])
  @@index([resource])
  @@index([user_id])
  @@index([admin_id])
  @@index([timestamp])
  @@index([resource, resource_id])
}

model SeoSettings {
  id               String   @id @default(uuid())
  title            String   @default("Vocab - Learn Vocabulary with AI")
  description      String   @default("Learn new vocabulary with AI assistance. Improve your English and Vietnamese vocabulary through spaced repetition and AI-generated content.")
  keywords         String   @default("vocabulary, learning, AI, English, Vietnamese, education, spaced repetition")
  og_title         String?
  og_description   String?
  og_image_url     String?
  og_type          String   @default("website")
  twitter_card     String   @default("summary_large_image")
  twitter_site     String?
  twitter_creator  String?
  canonical_url    String?
  robots           String   @default("index, follow")
  language         String   @default("en")
  theme_color      String   @default("#000000")
  background_color String   @default("#ffffff")
  created_at       DateTime @default(now())
  updated_at       DateTime @updatedAt

  @@map("seo_settings")
}

model WordPackage {
  id              String            @id @default(uuid())
  name            String
  description     String
  difficulty      Difficulty
  category        String
  tags            String[]
  word_count      Int
  is_active       Boolean           @default(true)
  created_at      DateTime          @default(now())
  updated_at      DateTime          @updatedAt
  source_language Language
  target_language Language
  user_selections UserWordPackage[]
  words           WordPackageWord[]

  @@map("word_packages")
}

model WordPackageWord {
  id              String      @id @default(uuid())
  word_package_id String
  term            String
  language        Language
  created_at      DateTime    @default(now())
  word_package    WordPackage @relation(fields: [word_package_id], references: [id], onDelete: Cascade)

  @@unique([word_package_id, term, language])
  @@map("word_package_words")
}

model UserWordPackage {
  id              String      @id @default(uuid())
  user_id         String
  word_package_id String
  selected_at     DateTime    @default(now())
  user            User        @relation(fields: [user_id], references: [id], onDelete: Cascade)
  word_package    WordPackage @relation(fields: [word_package_id], references: [id], onDelete: Cascade)

  @@unique([user_id, word_package_id])
  @@map("user_word_packages")
}

model CefrWord {
  id         String   @id @default(uuid())
  term       String   @unique
  level      String
  created_at DateTime @default(now())
  updated_at DateTime @updatedAt

  @@index([term])
  @@index([level])
}

model EngVieDict {
  id         String   @id @default(uuid())
  english    String   @unique
  created_at DateTime @default(now())
  updated_at DateTime @updatedAt
  ipa        String?
  pos        String?
  vietnamese String[]

  @@index([english])
}

enum Language {
  EN
  VI
}

enum Provider {
  TELEGRAM
  USERNAME_PASSWORD
  GOOGLE
}

enum Role {
  USER
  ADMIN
}

enum PartsOfSpeech {
  NOUN
  VERB
  ADJECTIVE
  ADVERB
  PRONOUN
  PREPOSITION
  CONJUNCTION
  INTERJECTION
}

enum Difficulty {
  BEGINNER
  INTERMEDIATE
  ADVANCED
}

enum Length {
  SHORT
  MEDIUM
  LONG
}

enum AuditAction {
  CREATE
  UPDATE
  DELETE
  LOGIN
  LOGOUT
  ACCESS
  EXPORT
  IMPORT
  APPROVE
  REJECT
  ENABLE
  DISABLE
  CLEAR_CACHE
  SYSTEM_CHECK
}
