/*
  Warnings:

  - A unique constraint covering the columns `[word_id,pos]` on the table `Definition` will be added. If there are existing duplicate values, this will fail.

*/

-- Step 1: Create a temporary column to store the first element of the pos array
ALTER TABLE "Definition" ADD COLUMN "pos_temp" "PartsOfSpeech";

-- Step 2: Update the temporary column with the first element of the pos array
UPDATE "Definition" SET "pos_temp" = (
  CASE
    WHEN array_length("pos", 1) > 0 THEN "pos"[1]
    ELSE 'NOUN'::"PartsOfSpeech"  -- Default to NOUN if array is empty
  END
);

-- Step 3: Handle potential duplicates by keeping only the first definition for each word_id + pos combination
-- Create a temporary table with unique combinations
CREATE TEMP TABLE unique_definitions AS
SELECT DISTINCT ON (word_id, pos_temp) id, word_id, pos_temp
FROM "Definition"
ORDER BY word_id, pos_temp, id;

-- Step 4: Delete duplicate definitions (keep only the ones in our unique table)
DELETE FROM "Definition"
WHERE id NOT IN (SELECT id FROM unique_definitions);

-- Step 5: Drop the old pos column
ALTER TABLE "Definition" DROP COLUMN "pos";

-- Step 6: Rename the temporary column to pos
ALTER TABLE "Definition" RENAME COLUMN "pos_temp" TO "pos";

-- Step 7: Set the column as NOT NULL
ALTER TABLE "Definition" ALTER COLUMN "pos" SET NOT NULL;

-- Step 8: Create the unique index
CREATE UNIQUE INDEX "Definition_word_id_pos_key" ON "Definition"("word_id", "pos");
