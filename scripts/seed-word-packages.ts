#!/usr/bin/env tsx

import { PrismaClient, Language, Difficulty, PartsOfSpeech } from '@prisma/client';
import { config } from 'dotenv';

// Load environment variables
config();

const prisma = new PrismaClient();

interface WordData {
	// Source word (being learned)
	source_term: string;
	source_language: Language;

	// Target word/explanation (in target language)
	target_term: string;
	target_language: Language;

	// Word details
	pos: PartsOfSpeech;
	ipa: string;
	images?: string[];

	// Explanations in both languages
	explains: {
		EN: string;
		VI: string;
	}[];

	// Examples in both languages
	examples: {
		EN: string;
		VI: string;
	}[];
}

interface WordPackageData {
	name: string;
	description: string;
	source_language: Language; // Language being learned
	target_language: Language; // Language for explanations
	difficulty: Difficulty;
	category: string;
	tags: string[];
	words: WordData[];
}

const SAMPLE_WORD_PACKAGES: WordPackageData[] = [
	// ===== ENGLISH PACKAGES (Learning English words with Vietnamese explanations) =====
	{
		name: 'Business English Essentials',
		description:
			'Essential vocabulary for professional business communication and workplace interactions',
		source_language: Language.EN, // Learning English words
		target_language: Language.VI, // Explained in Vietnamese
		difficulty: Difficulty.INTERMEDIATE,
		category: 'Business',
		tags: ['professional', 'workplace', 'communication', 'corporate'],
		words: [
			{
				source_term: 'negotiate',
				source_language: Language.EN,
				target_term: 'đàm phán',
				target_language: Language.VI,
				pos: PartsOfSpeech.VERB,
				ipa: '/nɪˈɡoʊʃieɪt/',
				explains: [
					{
						EN: 'To discuss something with someone in order to reach an agreement',
						VI: 'Thảo luận với ai đó để đạt được thỏa thuận',
					},
				],
				examples: [
					{
						EN: 'We need to negotiate the terms of the contract.',
						VI: 'Chúng ta cần đàm phán các điều khoản của hợp đồng.',
					},
				],
			},
			{
				source_term: 'proposal',
				source_language: Language.EN,
				target_term: 'đề xuất',
				target_language: Language.VI,
				pos: PartsOfSpeech.NOUN,
				ipa: '/prəˈpoʊzəl/',
				explains: [
					{
						EN: 'A plan or suggestion, especially a formal or written one',
						VI: 'Một kế hoạch hoặc đề nghị, đặc biệt là chính thức hoặc bằng văn bản',
					},
				],
				examples: [
					{
						EN: 'The board approved the new proposal.',
						VI: 'Hội đồng quản trị đã phê duyệt đề xuất mới.',
					},
				],
			},
			{
				source_term: 'deadline',
				source_language: Language.EN,
				target_term: 'thời hạn',
				target_language: Language.VI,
				pos: PartsOfSpeech.NOUN,
				ipa: '/ˈdedlaɪn/',
				explains: [
					{
						EN: 'A time or date by which something must be finished',
						VI: 'Thời gian hoặc ngày mà một việc gì đó phải được hoàn thành',
					},
				],
				examples: [
					{
						EN: 'The deadline for the project is next Friday.',
						VI: 'Thời hạn cho dự án là thứ Sáu tuần tới.',
					},
				],
			},
		],
	},
	// ===== VIETNAMESE PACKAGES (Learning Vietnamese words with English explanations) =====
	{
		name: 'Từ vựng Kinh doanh Cơ bản',
		description:
			'Từ vựng cần thiết cho giao tiếp kinh doanh và môi trường công sở chuyên nghiệp',
		source_language: Language.VI, // Learning Vietnamese words
		target_language: Language.EN, // Explained in English
		difficulty: Difficulty.INTERMEDIATE,
		category: 'Kinh doanh',
		tags: ['chuyên nghiệp', 'công việc', 'giao tiếp', 'doanh nghiệp'],
		words: [
			{
				source_term: 'đàm phán',
				source_language: Language.VI,
				target_term: 'negotiate',
				target_language: Language.EN,
				pos: PartsOfSpeech.VERB,
				ipa: '/ɗàm fán/',
				explains: [
					{
						EN: 'To discuss terms and conditions to reach an agreement',
						VI: 'Thảo luận các điều khoản và điều kiện để đạt được thỏa thuận',
					},
				],
				examples: [
					{
						EN: 'We need to negotiate the contract terms.',
						VI: 'Chúng ta cần đàm phán các điều khoản hợp đồng.',
					},
				],
			},
			{
				source_term: 'chiến lược',
				source_language: Language.VI,
				target_term: 'strategy',
				target_language: Language.EN,
				pos: PartsOfSpeech.NOUN,
				ipa: '/ciến lɯ̛ək/',
				explains: [
					{
						EN: 'A plan of action designed to achieve a long-term goal',
						VI: 'Kế hoạch hành động được thiết kế để đạt được mục tiêu dài hạn',
					},
				],
				examples: [
					{
						EN: 'Our marketing strategy needs to be updated.',
						VI: 'Chiến lược marketing của chúng ta cần được cập nhật.',
					},
				],
			},
			{
				source_term: 'doanh thu',
				source_language: Language.VI,
				target_term: 'revenue',
				target_language: Language.EN,
				pos: PartsOfSpeech.NOUN,
				ipa: '/zwaŋ tʰu/',
				explains: [
					{
						EN: 'Income generated from business operations',
						VI: 'Thu nhập được tạo ra từ hoạt động kinh doanh',
					},
				],
				examples: [
					{
						EN: "The company's revenue increased by 20% this year.",
						VI: 'Doanh thu của công ty tăng 20% trong năm nay.',
					},
				],
			},
		],
	},
];

async function createWordPackage(packageData: WordPackageData): Promise<void> {
	const { words, ...packageInfo } = packageData;

	try {
		// Check if package already exists
		const existingPackage = await prisma.wordPackage.findFirst({
			where: {
				name: packageData.name,
				source_language: packageData.source_language,
				target_language: packageData.target_language,
			},
		});

		if (existingPackage) {
			console.log(`📦 Word package '${packageData.name}' already exists. Skipping...`);
			return;
		}

		// Create the word package
		const wordPackage = await prisma.wordPackage.create({
			data: {
				...packageInfo,
				word_count: words.length,
				is_active: true,
			},
		});

		// Process each word
		for (const wordData of words) {
			// Create or find the source word (being learned)
			const sourceWord = await prisma.word.upsert({
				where: {
					term_language: {
						term: wordData.source_term,
						language: wordData.source_language,
					},
				},
				update: {},
				create: {
					term: wordData.source_term,
					language: wordData.source_language,
				},
			});

			// Create or find the target word (explanation)
			const targetWord = await prisma.word.upsert({
				where: {
					term_language: {
						term: wordData.target_term,
						language: wordData.target_language,
					},
				},
				update: {},
				create: {
					term: wordData.target_term,
					language: wordData.target_language,
				},
			});

			// Create definition for the source word
			const definition = await prisma.definition.create({
				data: {
					word_id: sourceWord.id,
					pos: wordData.pos,
					ipa: wordData.ipa,
					images: wordData.images || [],
				},
			});

			// Create explanations
			for (const explain of wordData.explains) {
				await prisma.explain.create({
					data: {
						definition_id: definition.id,
						EN: explain.EN,
						VI: explain.VI,
					},
				});
			}

			// Create examples
			for (const example of wordData.examples) {
				await prisma.example.create({
					data: {
						definition_id: definition.id,
						EN: example.EN,
						VI: example.VI,
					},
				});
			}

			// Add word to package (using target term as per your requirement)
			await prisma.wordPackageWord.create({
				data: {
					word_package_id: wordPackage.id,
					term: wordData.target_term,
					language: wordData.target_language,
				},
			});
		}

		console.log(`✅ Created word package: ${packageData.name} (${words.length} words)`);
	} catch (error) {
		console.error(`❌ Failed to create word package '${packageData.name}':`, error);
	}
}

async function seedWordPackages(): Promise<void> {
	console.log('🌱 Starting word package seeding...');

	try {
		// Connect to database
		await prisma.$connect();
		console.log('📦 Connected to database');

		// Create word packages
		for (const packageData of SAMPLE_WORD_PACKAGES) {
			await createWordPackage(packageData);
		}

		console.log('\n🎉 Word package seeding completed successfully!');
		console.log(`📊 Created ${SAMPLE_WORD_PACKAGES.length} word packages`);

		// Show summary
		const packageStats = await prisma.wordPackage.groupBy({
			by: ['source_language', 'target_language', 'difficulty'],
			_count: {
				_all: true,
			},
		});

		console.log('\n📋 Package Summary:');
		console.log('==================');
		packageStats.forEach((stat) => {
			console.log(
				`${stat.source_language} → ${stat.target_language} - ${stat.difficulty}: ${stat._count._all} packages`
			);
		});
	} catch (error) {
		console.error('❌ Word package seeding failed:', error);
		throw error;
	} finally {
		await prisma.$disconnect();
	}
}

// Main execution
async function main(): Promise<void> {
	await seedWordPackages();
}

// Run the script
if (require.main === module) {
	main().catch((error) => {
		console.error('Script execution failed:', error);
		process.exit(1);
	});
}

export { seedWordPackages };
